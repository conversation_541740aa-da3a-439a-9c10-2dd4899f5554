# 14000008 全账户查询

## 1. 接口概述

全账户查询接口，用于根据上送数据查询相应的账户信息。该接口支持多种查询场景：
- 根据挂失编号查询支取账户信息
- 通过账号查询所有主子账户信息  
- 根据对手账号查询内部户账户信息
- 支持多种筛选条件和输出标志

## 2. 代码执行流程图

```mermaid
flowchart TD
    A[客户端请求] --> B[Core14000008]
    B --> D[Core14000008Flow]
    D --> F[参数验证]
    F --> J{是否对手账号查询}
    
    J -->|是| K[查询对方账户信息]
    K --> K1[rbAcctContraInfoRepository.queryByContraBaseAcctNo]
    K1 --> K2[遍历处理每个账户]
    K2 --> K3[调用subAcctInfoDeal组装子账户信息]
    K3 --> K4[返回SubArray列表]
    
    J -->|否| L[查询账户客户关系]
    L --> L1[rbAcctClientRelationRepository.selectAcctInfoByPage]
    L1 --> L2[业务检查check]
    L2 --> N[获取账户信息]
    
    N --> P{是否需要查询子账户}
    P -->|是| Q[查询子账户列表]
    Q -->  S[遍历处理子账户，组装账户信息、余额、利息等]
    
    P -->|科目不为空| T[根据科目代码查询子账户]
    
    S --> U[账户限制标志检查]
    T --> U
    U --> V[组织客户联系信息，受益人信息等]
    V --> W[返回Core14000008Out]

    
    K4 --> W
    W --> X[响应客户端]
    
    style A fill:#e1f5fe
    style X fill:#c8e6c9
```

## 3. 数据流转分析图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant BS as Core14000008(BS层)
    participant BF as Core14000008Flow(BF层)
    participant Repo as Repository层
    participant RPC as 远程调用层
    participant Util as 工具组件层

    Client->>BS: HTTP请求 Core14000008In
    Note over BS: @CometMapping 路由映射
    BS->>BF: ExecutorFlow.startFlow("core14000008Flow")
    
    BF->>BF: acctBaseInquiry(Core14000008In)
    Note over BF: 开始主要业务逻辑
    
    %% 参数验证阶段
    BF->>Util: BusiUtil.isNull() 参数校验
    BF->>Util: EventBusServer.checkIccData() ICC数据校验
    
    %% 卡号处理阶段  
    BF->>Repo: rbCmCdAcctRepository.selectRbCmCdAcctBySubAcctNo()
    Repo-->>BF: RbCmCdAcct 卡账户关系
    BF->>Util: SendOfChannelMqNotice.getSfNewCardNoByOldCard()
    BF->>Util: IsCardBusiness.isCardByCardBin()
    
    %% 账户查询阶段
    alt 对手账号查询
        BF->>Repo: rbAcctContraInfoRepository.queryByContraBaseAcctNo()
        Repo-->>BF: List<RbAcctContraInfo>
        loop 每个对方账户
            BF->>Util: mbAcctInfoService.getRbAcctInfo()
            BF->>BF: subAcctInfoDeal() 组装子账户信息
        end
    else 正常账户查询
        BF->>Repo: rbAcctClientRelationRepository.selectAcctInfoByPage()
        Repo-->>BF: List<RbAcctClientRelation>
        BF->>Util: branchPermissionsCheck.check() 权限检查
        
        alt 挂失查询
            BF->>Repo: mbVoucherLostRepository.getMbVoucherLostByLostNo()
            Repo-->>BF: RbVoucherLost
        else 正常查询
            alt outFlag=11(销户)
                BF->>Repo: mbAcctRepository.getMbLeadAcct()
            else outFlag=33(关闭)  
                BF->>Repo: mbAcctRepository.getCloseAcctInfo()
            else 其他
                BF->>Repo: mbAcctRepository.getMbLeadAcct()
            end
            Repo-->>BF: RbAcct 主账户信息
        end
    end
    
    %% 主账户信息处理
    BF->>BF: mainAcctInfoDeal() 主账户信息处理
    BF->>RPC: CifRpc.getCifBaseInfo() 获取客户信息
    RPC-->>BF: CifBaseInfo
    BF->>Repo: mbAcctAttachRepository.getMbAcctAttach()
    BF->>Repo: mbAcctBalanceRepository.getRbAcctBalance()
    BF->>Repo: rbVoucherRepository.selectByBaseInfoEffective()
    BF->>Repo: mbPasswordRepository.getActivePassword() 
    
    %% 子账户查询处理
    opt 需要查询子账户
        alt outFlag=11
            BF->>Repo: mbAcctRepository.getCancelMbAcctByPage()
        else outFlag=33
            BF->>Repo: mbAcctRepository.getAllMbAcctInfoByPage()  
        else 其他
            BF->>Repo: mbAcctRepository.getMbAcctNoWarningByPage()
        end
        Repo-->>BF: List<RbAcct> 子账户列表
        
        BF->>BF: screenAcctData() 数据筛选
        
        loop 每个子账户
            BF->>BF: subAcctInfoDeal() 处理子账户
            BF->>RPC: CifRpc.getCifBaseInfo() 客户信息
            BF->>Repo: mbAcctBalanceRepository.getRbAcctBalance()
            BF->>Repo: mbAcctIntDetailRepository.getMbAcctIntDetailAll()
            BF->>Repo: mbAcctAttachRepository.getMbAcctAttach()
            BF->>Util: AvailBalCalclFactory.getAvailBalCalc() 余额计算
        end
    end
    
    %% 限制标志检查
    BF->>Repo: mbRestraintsRepository.getAllAcctRestraints()
    Repo-->>BF: List<RbRestraints>
    
    %% 其他信息装配
    BF->>Repo: rbAcctNatureDefRepository.getMbAcctNatureDefInfo()
    BF->>Repo: daoSupport.selectOne() 客户联系方式
    BF->>Repo: rbDefineColumnRepository.selectList() 自定义字段
    BF->>Util: mbAcctInfoService.getBenefitOwnerInfo() 受益人信息
    BF->>Repo: rbAcctStatsRepository.selectByInternalKeyAndMonth() 统计信息
    
    %% 数据转换
    BF->>Util: RbClientContactTblTo14000008Convert.INSTANCE.to()
    BF->>Util: RbDefineColumnInfoTo14000008Convert.INSTANCE.to()
    BF->>Util: AcctBenefitModelConvert.INSTANCE.toCore14000008Out()
    
    BF-->>BS: Core14000008Out
    BS-->>Client: HTTP响应
    
    Note over BF: 业务逻辑处理完成
```

## 4. 各功能组件

### 4.1 服务入口层 (BS)

**Core14000008 **

- **实现**: 将请求转发到Flow层处理

### 4.2 业务流程层 (BF)

**Core14000008Flow **

**核心业务逻辑**:
1. **参数验证**: 检查baseAcctNo和contraAcctNo至少一个不为空
2. **ICC数据校验**: 验证IC卡数据完整性
3. **卡号处理**: 支持新旧卡号转换，判断是否为卡片业务
4. **多场景查询支持**:
   - 对手账号查询: 查询内部户对应账户
   - 挂失账号查询: 根据挂失编号查询可支取账户
   - 正常账户查询: 根据账号查询主子账户
5. **数据筛选**: 根据screenFlag参数进行多维度筛选
6. **信息装配**: 组装客户、凭证、余额、利息等完整信息

