# 14000207限制信息查询

## 1. 接口概述

**接口描述**: 通过本服务进行客户级和账户级的限制查询。此服务可查询客户存款账户的账户级限制和客户级限制，如果账户级限制为司法冻结，可查看限制和解限有权机关信息。  

## 2. 代码执行流程图

```mermaid
flowchart TD
    A[客户端请求] --> B[Core14000207]
    B -->  D[Core14000207Flow]
    
    D --> E{判断baseAcctNo}
    E -->|非空| F[处理账户级查询]
    E -->|为空| G{判断clientNo}
    
    F --> F1[判断是否银行卡]
    F1 -->|是卡| F2[CdCardArchRepository查询]
    F1 -->|非卡| F3[直接使用baseAcctNo]
    
    F2 --> F4[获取账户信息]
    F3 --> F4
    F4 --> F5[RbAcctRepository.getMbAcctInfoByPrimaryKeyList]
    F5 --> F6[RbBaseAcctRepository.getMbBaseAcctByBaseAcctNoOrCardNo]
    F6 --> F7[转换为标准账户模型]
    F7 --> F8[BranchPermissionsCheck.check]
    F8 --> F9[RbAcctBalanceRepository.getRbAcctBalance]
    F9 --> F10[CifRpc.getCifBaseInfo]
    F10 --> F11[AvailBalCalclFactory.getAvailBal]
    F11 --> F12[查询账户限制信息]
    F12 --> F13[RbRestraintsRepository.getRestraintsForPage]
    F13 --> F14[RbRestraintsHistRepository.selectByResSeqNoAndClientNo]
    F14 --> F15[FmUtil.getFmRestraintType]
    F15 --> F16[MbAcctInfoService.getRbAcctInfo]
    F16 --> F17[组装账户级限制结果]
    
    G -->|非空| H[处理客户级查询]
    G -->|为空| I{判断证件信息}
    
    H --> H1[CifRpc.getCifBaseInfo]
    H1 --> H2[CifRpc.getCifRestrainsInfo]
    H2 --> H3[BeanUtil.listCopy]
    H3 --> H4[设置限制优先级]
    H4 --> H5[过滤限制状态]
    H5 --> H17[返回客户级限制结果]
    
    I -->|证件信息完整| J[通过证件查询客户]
    I -->|信息不完整| K[返回空结果]
    
    J --> J1[CifRpc.getCifBaseInfoByDocId]
    J1 --> J2[验证客户类型]
    J2 -->|个人客户| J3[获取客户限制信息]
    J2 -->|对公客户| J4[抛出异常]
    J3 --> J5[CifRpc.getCifRestrainsInfo]
    J5 --> J6[过滤和组装结果]
    J6 --> J17[返回证件查询结果]
    
    F17 --> Z[返回最终结果]
    H17 --> Z
    J17 --> Z
    K --> Z
    J4 --> ERROR[业务异常]
    
    style A fill:#e1f5fe
    style Z fill:#c8e6c9
    style ERROR fill:#ffcdd2
```

## 3. 数据流转分析图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant BS as Core14000207
    participant Flow as Core14000207Flow
    participant CardRepo as CdCardArchRepository
    participant AcctRepo as RbAcctRepository 
    participant BaseAcctRepo as RbBaseAcctRepository
    participant BalRepo as RbAcctBalanceRepository
    participant ResRepo as RbRestraintsRepository
    participant ResHistRepo as RbRestraintsHistRepository
    participant CIF as CifRpc
    participant FmUtil as FmUtil
    participant AcctService as MbAcctInfoService
    
    Client->>BS: 发送查询请求(Core14000207In)
    BS->>Flow: startFlow("core14000207Flow", in)
    
    alt 存在baseAcctNo
        Flow->>CardRepo: 判断是否银行卡
        CardRepo-->>Flow: 返回卡信息
        
        Flow->>AcctRepo: 查询账户信息
        AcctRepo-->>Flow: 返回账户列表
        
        Flow->>BaseAcctRepo: 查询基础账户信息
        BaseAcctRepo-->>Flow: 返回基础账户列表
        
        Flow->>BalRepo: 查询账户余额
        BalRepo-->>Flow: 返回余额信息
        
        Flow->>CIF: 查询客户信息
        CIF-->>Flow: 返回客户基本信息
        
        Flow->>ResRepo: 查询限制信息
        ResRepo-->>Flow: 返回限制信息列表
        
        loop 处理每个限制记录
            Flow->>ResHistRepo: 查询限制历史
            ResHistRepo-->>Flow: 返回历史记录
            
            Flow->>FmUtil: 获取限制类型描述
            FmUtil-->>Flow: 返回类型描述
            
            Flow->>AcctService: 获取账户标准信息
            AcctService-->>Flow: 返回标准账户模型
        end
        
    else 存在clientNo
        Flow->>CIF: 查询客户基本信息
        CIF-->>Flow: 返回客户信息
        
        Flow->>CIF: 查询客户限制信息
        CIF-->>Flow: 返回限制信息
        
    else 存在证件信息
        Flow->>CIF: 通过证件查询客户
        CIF-->>Flow: 返回客户信息
        
        alt 个人客户
            Flow->>CIF: 查询客户限制信息
            CIF-->>Flow: 返回限制信息
        else 对公客户
            Flow-->>Client: 抛出业务异常
        end
    end
    
    Flow->>Flow: 组装返回结果
    Flow-->>BS: 返回Core14000207Out
    BS-->>Client: 返回查询结果
```

## 4. 功能组件

### 4.1 服务入口层 (bs)

**Core14000207**

- **功能**: 作为限制信息查询服务的统一入口

### 4.2 业务流程层 (bf)

**Core14000207Flow**

- **功能**: 限制信息查询的核心业务流程处理

- **核心实现**:

  #### 4.2.1 账户级查询逻辑 (baseAcctNo存在)
  - **银行卡检查**: 通过`IIsCardBusiness.isCardByCardBin()`判断输入是否为银行卡
  - **卡号转账号**: 若为银行卡，通过`CdCardArchRepository`和`RbAcctClientRelationRepository`获取实际账号
  - **账户信息获取**: 
    - `RbAcctRepository.getMbAcctInfoByPrimaryKeyList()` - 获取账户详细信息
    - `RbBaseAcctRepository.getMbBaseAcctByBaseAcctNoOrCardNo()` - 获取基础账户信息
  - **权限检查**: `BranchPermissionsCheck.check()` - 验证机构访问权限
  - **余额查询**: `RbAcctBalanceRepository.getRbAcctBalance()` - 获取账户余额
  - **可用余额计算**: `AvailBalCalclFactory.getAvailBalCalc()` - 计算可用余额

  #### 4.2.2 限制信息处理
  - **限制查询**: `RbRestraintsRepository.getRestraintsForPage()` - 分页查询限制信息
  - **历史记录**: `RbRestraintsHistRepository.selectByResSeqNoAndClientNo()` - 获取限制变更历史
  - **类型描述**: `FmUtil.getFmRestraintType()` - 获取限制类型描述信息
  - **账户标准化**: `MbAcctInfoService.getRbAcctInfo()` - 转换为标准账户模型

  #### 4.2.3 客户级查询逻辑 (clientNo存在)
  - **客户信息**: `CifRpc.getCifBaseInfo()` - 获取客户基础信息
  - **限制查询**: `CifRpc.getCifRestrainsInfo()` - 获取客户级限制信息
  - **数据转换**: `BeanUtil.listCopy()` - 对象转换和复制

  #### 4.2.4 证件查询逻辑 (证件信息存在)
  - **客户查找**: `CifRpc.getCifBaseInfoByDocId()` - 通过证件信息查找客户
  - **类型验证**: 检查客户类型，对公客户不允许通过证件查询
  - **限制获取**: 获取个人客户的限制信息



