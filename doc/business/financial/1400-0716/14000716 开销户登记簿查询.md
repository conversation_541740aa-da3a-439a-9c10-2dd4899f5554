# 14000716 开销户登记簿查询

## 1. 接口概述

开销户登记簿查询接口，用于客户临柜查询个人账户开销户详细信息。该接口支持根据多种条件查询账户的开户、销户和预开户信息。

## 2. 代码执行流程图

```mermaid
flowchart TD
    A[客户端请求] --> B[Core14000716]
    B --> D[Core14000716Flow]
    
    D --> E[参数提取与验证校验]
    E --> H{判断登记类型}
    
    H -->|regType=3 预开户| I1[查询预开户信息]
    I1 --> I1_1[mbOpenCloseAcctRepository.getPreOpenCloseAcctRegList]
    
    H -->|regType=2 销户| I2[查询销户信息]
    I2 --> I2_1[mbOpenCloseAcctRepository.getCloseAcctRegList]
    
    H -->|regType=1 开户| I3[查询开户信息]
    I3 --> I3_1[mbOpenCloseAcctRepository.getOpenCloseAcctRegListRb]
    
    I1_1 --> J{遍历查询结果}
    I2_1 --> J
    I3_1 --> J
    
   
    J --> M{账户序号是否为0}
    M -->|是| M1[处理基础账户逻辑]
    M -->|否| M2[获取账户信息， 并账户过滤]
    
    M1 --> M1_1[获取账户信息]
    M1_1 --> M1_4[各种业务条件过滤]
    M1_4 --> M1_5[组装基础账户输出信息]
    
    M2 -->  P[业务信息装配]
   
    
    P --> P1[设置激活状态和日期]
    P --> P2[处理绑定账户信息]
    P --> P3[设置基本账户信息]
    P --> P4[根据登记类型设置特定信息]
    
    P1 -->  M1_5
    P2 -->  M1_5
    P3 -->  M1_5
    P4 -->  M1_5
  
    M1_5 --> Y[响应客户端]
    
    style A fill:#e1f5fe
    style Y fill:#c8e6c9
   
  
```

## 3. 数据流转分析图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant BS as Core14000716(BS层)
    participant BF as Core14000716Flow(BF层)
    participant Repo as Repository层
    participant RPC as 远程调用层
    participant Util as 工具组件层

    Client->>BS: HTTP请求 Core14000716In
    Note over BS: @CometMapping 路由映射
    BS->>BF: ExecutorFlow.startFlow("core14000716Flow")
    
    BF->>BF: execute(Core14000716In)
    Note over BF: 开始主要业务逻辑
    
    %% 参数验证阶段
    BF->>Util: BusiUtil.isNull() 参数校验
    BF->>Util: DateUtil.parseDate() 日期解析验证
    BF->>Util: DateUtil.addMonths() 日期跨度验证
    
    %% 根据登记类型查询数据
    alt regType=3 (预开户)
        BF->>Repo: mbOpenCloseAcctRepository.getPreOpenCloseAcctRegList()
        Repo-->>BF: List<RbOpenCloseReg>
    else regType=2 且 programId=2807 (销户)
        BF->>Repo: mbOpenCloseAcctRepository.getCloseAcctRegList()
        Repo-->>BF: List<RbOpenCloseReg>
    else 其他情况 (开销户)
        BF->>Repo: mbOpenCloseAcctRepository.getOpenCloseAcctRegListRb()
        Repo-->>BF: List<RbOpenCloseReg>
    end
    
    %% 检查查询结果
    alt 查询结果为空
        BF->>Util: BusiUtil.createBusinessException("MB4018")
    else 有查询结果
        loop 每条登记记录
            alt 账户序号 != "0" (子账户)
                BF->>Repo: mbAcctRepository.getMbAcctByInternalKey()
                Repo-->>BF: RbAcct 账户信息
                
                opt 账户信息存在
                    %% 内部户验证
                    alt isInnerAcct="Y"
                        BF->>BF: 验证sourceModule="GL"
                    else 普通账户
                        BF->>BF: 验证个体客户标志
                        BF->>BF: 验证客户类型匹配
                    end
                    
                    %% 凭证信息处理
                    alt 个人客户且非主账户
                        BF->>Repo: mbBaseAcctRepository.getBaseAcct()
                        Repo-->>BF: RbBaseAcct 基础账户
                        BF->>BF: 设置凭证类型和号码
                    else 其他情况
                        BF->>BF: 直接使用账户凭证信息
                    end
                    
                    %% 绑定账户查询(非一类户)
                    opt 账户类别 != "1"
                        BF->>Repo: mbAcctSettleRepsitory.selectAcctSettleByIntSettleAcctClass()
                        Repo-->>BF: RbAcctSettle 结算关系
                        BF->>BF: 设置绑定账户信息
                    end
                    
                    %% 装配账户详细信息
                    BF->>Util: DateUtil.formatDate() 格式化各种日期
                    BF->>BF: 设置账户基本信息
                    BF->>BF: 根据登记类型设置特定字段
                    
                    %% 双零清理标志过滤
                    alt zeroCloseInd="Y"
                        BF->>Util: BusiUtil.getMessageByKey("MG0056")
                        alt 摘要匹配双零清理
                            BF->>BF: 添加到结果列表
                        end
                    else zeroCloseInd="N"
                        alt 摘要不是双零清理
                            BF->>BF: 添加到结果列表
                        end
                    else zeroCloseInd为空
                        BF->>BF: 添加到结果列表
                    end
                end
                
            else 账户序号 = "0" (基础账户)
                %% 获取实际账号
                BF->>Repo: rbAcctClientRelationRepository.selectListAcct()
                Repo-->>BF: List<RbAcctClientRelation>
                
                %% 查询基础账户
                BF->>Repo: mbBaseAcctRepository.getBaseAcct()
                Repo-->>BF: RbBaseAcct 基础账户信息
                
                opt 基础账户存在
                    %% 获取产品信息
                    BF->>RPC: ProductRpc.getRbProduct()
                    RPC-->>BF: RbProduct 产品信息
                    
                    %% 各种业务条件验证
                    BF->>BF: 验证账户状态、个体标志、内部户标志
                    BF->>Util: DateUtil.formatDate() 日期格式化
                    BF->>BF: 组装基础账户信息
                    BF->>BF: 添加到结果列表
                end
            end
        end
        
        %% 检查最终结果
        alt 结果列表为空
            BF->>Util: BusiUtil.createBusinessException("MB4018")
        else 有结果
            BF->>BF: 创建Core14000716Out
            BF->>BF: 设置openCloseAcctArray
        end
    end
    
    BF-->>BS: Core14000716Out
    BS-->>Client: HTTP响应
    
    Note over BF: 业务逻辑处理完成
```

## 4. 各功能组件

### 4.1 服务入口层 (BS)

**Core14000716**

- **实现**: 将请求转发到Flow层处理

### 4.2 业务流程层 (BF)

**Core14000716Flow 核心业务逻辑**:

#### 4.2.1 参数验证
- **开始日期验证**: 不能为空
- **结束日期验证**: 不能为空
- **日期顺序验证**: 开始日期不能晚于结束日期
- **日期范围验证**: 结束日期不能超过当前日期
- **日期跨度验证**: 查询跨度不能超过3个月
- **登记类型验证**: 必须提供

#### 4.2.2 数据查询策略
根据不同的登记类型(regType)采用不同的查询策略：

1. **预开户查询 (regType=3)**:
   - 调用`getPreOpenCloseAcctRegList()`方法
   - 查询预开户登记信息

2. **销户查询 (regType=2 )**:
   - 调用`getCloseAcctRegList()`方法
   - 查询销户登记信息

3. **开户查询 (regType=1)**:
   - 调用`getOpenCloseAcctRegListRb()`方法
   - 查询开户和销户登记信息

#### 4.2.3 数据处理逻辑

**基础账户处理 (账户序号=0)**:
1. 通过账户客户关系表获取实际账号
2. 查询基础账户信息
3. 通过ProductRpc获取产品信息，验证源模块
4. 进行各种业务条件过滤
5. 组装账户基本信息

**子账户处理 (账户序号≠0)**:
1. 根据内部键查询账户详细信息
2. 内部户标志验证：内部户只显示GL模块账户
3. 个体客户标志验证：确保客户类型匹配
4. 凭证信息处理：个人非主账户需查询基础账户凭证
5. 绑定账户处理：非一类户需查询绑定的一类户信息
6. 账户状态和激活状态设置
7. 根据登记类型设置特定的日期和状态信息

