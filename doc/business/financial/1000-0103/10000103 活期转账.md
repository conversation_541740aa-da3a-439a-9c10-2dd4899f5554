# Core10000103 活期转账交易

## 概述

 活期转账交易的完整代码执行流程，包括每个节点的具体实现逻辑、调用链路和业务处理过程。

## 1. 交易入口

### 1.1 ICore10000103服务接口定义

**URL**: `/rb/fin/current/tra`  

**服务编码**: `MbsdCore-1000-0103`  

**功能描述**: 活期转账

### 1.2 Core10000103服务实现入口

## 2. 核心业务流程分析

**业务数据流转**

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Service as Core10000103
    participant Flow as Core10000103Flow
    participant Gravity as CurrentAcctTraGroup
    participant TAE as TAE引擎
    participant DB as 数据库

    Client->>Service: HTTP请求
    Service->>Flow: startFlow
    Flow->>DB: 查询账户信息
    DB-->>Flow: 返回账户数据
    Flow->>Flow: identifyAcctStd
    Flow->>Gravity: executeGravity
    Gravity->>DB: 限制检查
    Gravity->>DB: 余额检查
    Gravity->>TAE: 异步记账
    TAE-->>Gravity: 记账结果
    Gravity-->>Flow: 执行结果
    Flow-->>Service: 业务结果
    Service-->>Client: HTTP响应
```



### 2.1 Core10000103Flow 业务流程

#### 2.1.1 identifyAcctStd 账户标准化识别

**执行逻辑**:

1.  交易开始，初始化相关变量
2. 处理旧卡交易逻辑
3. 获取消费透支卡协议信息
4. 获取转出和转入账户信息
5. 账户校验和金额验证

#### 2.1.2 execute 核心执行方法

## 3. Gravity 组件流程分析

Gravity 流程包含以下组件：

```mermaid
graph TB
    %% 定义样式类
    classDef startStyle fill:#e8f5e8,stroke:#4caf50,stroke-width:3px,color:#1b5e20,font-weight:bold
    classDef processStyle fill:#e3f2fd,stroke:#2196f3,stroke-width:2px,color:#0d47a1,font-weight:500
    classDef decisionStyle fill:#fff3e0,stroke:#ff9800,stroke-width:2px,color:#e65100,font-weight:500
    classDef errorStyle fill:#ffebee,stroke:#f44336,stroke-width:2px,color:#b71c1c,font-weight:500
    classDef successStyle fill:#e8f5e8,stroke:#4caf50,stroke-width:3px,color:#1b5e20,font-weight:bold
    

A[开始]:::startStyle
B[结束]:::successStyle
Check1[定向支付检查IRbTranCheckBg.checkRbAcctPayment组件]:::processStyle
Check2[定活期账号检查IRbCheckBg.checkAcctType组件]:::processStyle
Check3[交易校验流程组-票据信息检查BusiTranChechGroup.checkBillInfo组件]:::processStyle
Check4[书挂检查IRbTranCheck.checklostVoucher组件]:::processStyle
Check5[客户校验流程组-客户基本检查BusiClientCheckGroup.checkClientBasis组件]:::processStyle
AcctCheck[账户校验流程组-账户基本校验BusiAcctCheckGroup.checkAcctBasis组件]:::processStyle

idt[证件检查BusiAcctCheckGroup.checkDocument组件]:::processStyle
pwd[验密BusiAcctCheckGroup.checkAcctWithdrawType组件]:::processStyle
corss[跨境资金池账户组校验PcpCrossCheckBg.pcpCrossCheck组件]:::processStyle
onns[一户通账户转账校验YhtAcctCheckBg.pcpCrossCheck组件]:::processStyle
sub[交易校验流程组-分户限制校验BusiTranCheckGroup.checkAllRestraints组件]:::processStyle
hodat[交易校验流程组-节假日校验BusiTranCheckGroup.checkHoliday组件]:::processStyle

cond{开立检查}:::decisionStyle
check21[个人借记卡下开立零余额外币子账户IMbAcctMainApplicationBg.openForeAcct组件]
AcctCheck1[账户校验流程组-账户基本校验BusiAcctCheckGroup.checkAcctBasis组件]
CA[外币转账检查转入转出余额类型IRbTranCheckBg.checkInAndOutAcctBalType组件]
CC[内部户转账交易检查IRbTranCheckBg.glAcctTranCheck组件]
currentAcctTra[活期转账CurrentAcctTraGroup.currentAcctTra组件]
currentAcctTra0[活期转账自动开外币子账户CurrentAcctTraGroup.currentAcctTra0组件]

A --> Check1 --> Check2 --> Check3 --> Check4 --> Check5
Check5 --> AcctCheck --> idt --> pwd --> corss --> onns --> sub --> hodat
hodat --> cond
cond -->|OpenFlag = N| check21
cond -->|OpenFlag = Y| currentAcctTra0
check21 --> AcctCheck1 --> CA --> CC --> currentAcctTra
currentAcctTra --> B
currentAcctTra0 --> B
```



### 3.1 CurrentAcctTraGroup 活期转账组件

#### 3.1.1 活期转账

**执行逻辑**:

1. **账户信息获取**: 获取转出和转入账户标准信息
2. **限制检查**: 执行账户限制和冻结检查
3. **手续费计算**: 计算转账手续费
4. **交易记录构建**: 构建 TAE 记账明细
5. **异步记账**: 调用 TAE 引擎进行异步记账

#### 3.1.2 活期转账自动开外币子账户

**执行逻辑**:

1. **账户信息获取**: 获取交易账户信息
2. **免密检查**: 执行借记小额免密检查
3. **费用计算**: 组织费用信息
4. **开立子账户**：开立非零余额外币子账户



## 4. TEA回调

```mermaid
graph TD
    A[TAE异步记账请求] --> B[AsynFinancialImpl]
    B --> D[AsynFinancialFlow.execute]
   
    D --> N{tranCode类型判断}
    N -->|普通活期贷记:NormalCurrentCret| O[AsynNormalCurrentCretFlow.process]
    N -->|普通活期借记:NormalCurrentDebt| O1[AsynNormalCurrentDebtFlow.process]
    N -->|其他类型| P[其他Flow处理]
 
    O --> Q[初始化和参数设置]
    Q --> R[设置上下文信息]
    R --> W{交易类型判断}
    
    W -->|活期借记服务| X[currentTranService.debtTransaction]
    W -->|活期存入服务| Y[currentTranService.cretTransaction]
    

  	O1 --> Q1[初始化和参数设置]
    Q1 --> R1[设置上下文信息]
    R1 -->|活期支取服务| X1[currentTranService.debtTransaction]

    Y --> BB[intTaxPorcess利息税处理]
    
    X --> CC[借记后处理]
    X1 --> CC1[借记后处理]

    BB --> DD[贷记后处理]
    CC --> EE[交易状态更新]
    DD --> EE
    CC1 --> EE1[交易状态更新]
  
    
    EE --> FF[后置处理]
    FF --> PP[构建AsynFinancialOut]
    PP --> QQ[返回结果]
    
    EE1 --> FF1[后置处理]
    FF1 --> PP1[构建AsynFinancialOut]
    PP1 --> QQ1[返回结果]
    
    
    style A fill:#90EE90
    style QQ fill:#FFB6C1
    style QQ1 fill:#FFB6C1
    style O fill:#87CEEB
    style O1 fill:#87CEEB
    style X fill:#DDA0DD
    style X1 fill:#DDA0DD
    style Y fill:#F0E68C


```

### 4.1 IAsynFinancial 接口定义

**关键信息**:

- 服务URL: `/rb/fin/asyn/financial
- 存款异步记账统一入口，TAE回调统一入口具体实现由交易场景独立实现

### 4.2 AsynFinancialFlow服务实现

1. 设置子流水号
2. 通过工厂获取对应的TAE流程处理器
3. 转换请求参数
4. 执行具体业务流程
5. 账户检查处理

### 4.3 AsynNormalCurrentCretFlow服务实现

### 4.4 AsynNormalCurrentDebtFlow服务实现
