# 14000113 凭证信息查询

## 1. 接口概述

**功能描述**: 根据账号、证件类型、证件号、客户号、国家、凭证类型等，查询客户下的凭证信息

## 2. 代码执行流程图

```mermaid
flowchart TD
    A[客户端请求] --> B[Core14000113]
    B --> D[Core14000113Flow]
    
    D --> E{检查输入参数类型}
    E -->|证件查询| F[通过证件信息查询客户信息]
    E -->|账号查询| G[通过账号查询]
    E -->|客户号查询| H[通过客户号查询客户信息]
    
    F --> F1[查询客户下所有未关闭的账户]
    F1 --> F2[查询所有账户凭证信息]
    F2 --> F3[补充附属卡凭证信息]
    F3 --> I[构建凭证信息]
    
    G --> G1{判断是否为卡}
    G1 -->|卡| G2[获取卡片信息]
    G1 -->|账号| G3[获取账户信息]
    G2 --> G4{判断附属卡标志}
    G4 -->|附属卡| G5[findSubCard]
    G4 -->|主卡| G6[获取主卡账户]
    G3 --> G7[获取主子账户凭证详情]
    G5 --> I
    G6 --> I
    G7 --> I
    
    H --> H1[查询客户下所有未关闭的账户]
    H1 --> H2[查询所有账户凭证信息]
    H2 --> I
    
    I -->  M[构建DetailArray]
    M --> O[docNum统计支票数量]
    O --> P[返回结果]
```

## 3. 数据流转分析图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant BS as Core14000113(bs层)
    participant BF as Core14000113Flow(bf层)
    participant CIF as CifRpc(客户信息)
    participant BC as 业务组件层
    participant Repo as 数据访问层
    participant DB as 数据库
    
    Client->>BS: Core14000113In请求
    BS->>BF: 调用core14000113Flow
    
    BF->>BF: 解析输入参数
    
    alt 证件信息查询
        BF->>CIF: getCifBaseInfoByDocId
        CIF-->>BF: 返回客户信息
        BF->>Repo: getMbAcctByClientNo
        Repo->>DB: 查询账户信息
        DB-->>Repo: 返回账户数据
        Repo-->>BF: 返回账户列表
    else 账号查询
        BF->>BC: isCardByCardBin判断卡号
        BC-->>BF: 返回判断结果
        alt 是卡号
            BF->>Repo: getCardInfoByBaseAcctNo
            Repo->>DB: 查询卡片信息
            DB-->>Repo: 返回卡片数据
            Repo-->>BF: 返回卡片信息
            BF->>BC: getLeadBaseAcctOrAcct
            BC-->>BF: 返回主账户信息
        else 是账号
            BF->>BC: getLeadBaseAcctOrAcct
            BC->>Repo: 查询账户关系
            Repo->>DB: 查询数据库
            DB-->>Repo: 返回数据
            Repo-->>BC: 返回账户信息
            BC-->>BF: 返回标准账户模型
        end
    else 客户号查询
        BF->>CIF: getCifBaseInfo
        CIF-->>BF: 返回客户信息
        BF->>Repo: getMbAcctAllByClientNo
        Repo->>DB: 查询客户账户
        DB-->>Repo: 返回账户数据
        Repo-->>BF: 返回账户列表
    end
    
    BF->>BF: findMbAcctByBaseAcctNo处理账户
    BF->>Repo: 查询印鉴关系
    Repo->>DB: selectMbSealRelation
    DB-->>Repo: 返回印鉴数据
    Repo-->>BF: 返回印鉴关系
    
    BF->>BF: getVoucherInfoAll获取凭证信息
    BF->>Repo: getMbVoucherAcctRelationInfo
    Repo->>DB: 查询凭证账户关系
    DB-->>Repo: 返回凭证数据
    Repo-->>BF: 返回凭证关系列表
    
    BF->>BF: 构建DetailArray明细
   ## BF->>BF: docGroup去重处理
    BF->>BF: docNum统计支票数量
    
    BF-->>BS: 返回Core14000113Out
    BS-->>Client: 返回查询结果
```

## 4. 功能组件

### 4.1 服务入口层 (bs)

**功能描述**:

- 将请求转发给Flow层处理

### 4.2 业务流程层 (bf)

**功能描述**:

- 根据不同的输入参数类型（证件信息、账号、客户号）采用不同的查询策略
- 整合多个数据源的信息，包括客户信息、账户信息、卡片信息、凭证信息等

### 4.3 业务组件层 (bc)

#### 4.3.1 账户信息组件

**功能描述**:
- 提供统一的账户信息获取接口
- 处理卡号和账号的差异，支持卡折一体化
- 实现主账户查询逻辑

#### 4.3.2 卡片业务组件

**功能描述**:
- 判断输入的号码是否为卡号
- 获取卡片类型信息

#### 4.3.3 凭证操作组件

**功能描述**:
- 提供凭证账户关系的操作
- 支持凭证状态变更和关系维护

